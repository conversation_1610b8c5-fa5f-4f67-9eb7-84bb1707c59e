<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SideView Panel</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* Default theme variables - will be overridden by theme manager */
            --panel-background: #2d2d30;
            --panel-background-secondary: #3e3e42;
            --panel-border: #464647;
            --text-primary: #ffffff;
            --text-secondary: #cccccc;
            --text-muted: #999999;
            --accent-color: #0078d4;
            --accent-hover: #106ebe;
            --button-background: #0e639c;
            --button-hover: #1177bb;
            --button-text: #ffffff;
            --app-item-hover: rgba(255, 255, 255, 0.1);
            --app-item-active: rgba(0, 120, 212, 0.3);
            --scrollbar-track: rgba(255, 255, 255, 0.1);
            --scrollbar-thumb: rgba(255, 255, 255, 0.3);
            --scrollbar-thumb-hover: rgba(255, 255, 255, 0.5);
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--panel-background);
            color: var(--text-primary);
            height: 100vh;
            overflow: hidden;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            margin: 0;
            padding: 0;
        }

        .panel-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            border-radius: 16px 0 0 16px;
            border: 2px solid var(--panel-border);
            border-right: none;
            box-shadow: -4px 0 20px rgba(0, 0, 0, 0.4), 
                        -2px 0 10px rgba(0, 0, 0, 0.2),
                        inset 1px 1px 0 rgba(255, 255, 255, 0.1);
            overflow: hidden;
            background: linear-gradient(135deg, var(--panel-background) 0%, var(--panel-background-secondary) 100%);
        }

        .panel-header {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 60px; /* Fixed height to match TypeScript constant */
            padding: 0 8px; /* Reduced padding for narrow design */
            background: linear-gradient(135deg,
                        rgba(255, 255, 255, 0.08) 0%,
                        rgba(255, 255, 255, 0.02) 50%,
                        transparent 100%);
            border-bottom: 1px solid var(--panel-border);
            border-radius: 16px 0 0 0;
            -webkit-app-region: drag;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15),
                        inset 0 1px 0 rgba(255, 255, 255, 0.1);
            position: relative;
            box-sizing: border-box; /* Include border in height calculation */
        }

        .panel-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, 
                        transparent 0%, 
                        rgba(255, 255, 255, 0.2) 50%, 
                        transparent 100%);
        }

        .panel-title {
            font-size: 10px;
            font-weight: 600;
            color: var(--text-primary);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            letter-spacing: 0.3px;
            margin-bottom: 2px;
        }

        .panel-controls {
            display: flex;
            gap: 4px;
            -webkit-app-region: no-drag;
            flex-wrap: wrap;
            justify-content: center;
        }

        .control-button {
            width: 20px;
            height: 20px;
            border: none;
            border-radius: 6px;
            background: linear-gradient(135deg, var(--button-background) 0%,
                        color-mix(in srgb, var(--button-background) 80%, black) 100%);
            color: var(--button-text);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2),
                        0 1px 2px rgba(0, 0, 0, 0.1),
                        inset 0 1px 0 rgba(255, 255, 255, 0.15);
            position: relative;
            overflow: hidden;
        }

        .control-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, 
                        transparent, 
                        rgba(255, 255, 255, 0.2), 
                        transparent);
            transition: left 0.5s ease;
        }

        .control-button:hover {
            background: linear-gradient(135deg, var(--button-hover) 0%, 
                        color-mix(in srgb, var(--button-hover) 80%, black) 100%);
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.25),
                        0 2px 6px rgba(0, 0, 0, 0.15),
                        inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .control-button:hover::before {
            left: 100%;
        }

        .control-button:active {
            transform: translateY(-1px) scale(1.02);
        }

        .control-button.pinned {
            background: linear-gradient(135deg, var(--accent-color) 0%, 
                        color-mix(in srgb, var(--accent-color) 80%, black) 100%);
            box-shadow: 0 4px 12px rgba(0, 120, 212, 0.4),
                        0 2px 6px rgba(0, 120, 212, 0.2),
                        inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .app-list {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 0 0 auto;
            max-height: 200px;
            overflow-y: auto;
            border-bottom: 1px solid var(--panel-border);
            padding: 8px 4px;
        }

        .app-item {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 8px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            position: relative;
            overflow: hidden;
            border-radius: 8px;
            margin: 4px;
            width: 48px;
            height: 48px;
        }

        .app-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 3px;
            height: 100%;
            background: transparent;
            transition: all 0.3s ease;
        }

        .app-item:hover {
            background: linear-gradient(90deg, 
                        var(--app-item-hover) 0%, 
                        rgba(255, 255, 255, 0.05) 100%);
            transform: translateX(4px);
            box-shadow: inset 0 0 20px rgba(255, 255, 255, 0.05);
        }

        .app-item:hover::before {
            background: linear-gradient(180deg, 
                        var(--accent-color) 0%, 
                        color-mix(in srgb, var(--accent-color) 70%, white) 100%);
        }

        .app-item.active {
            background: linear-gradient(90deg, 
                        var(--app-item-active) 0%, 
                        rgba(0, 120, 212, 0.15) 100%);
            border-left: none;
            box-shadow: inset 4px 0 0 var(--accent-color), 
                        0 2px 12px rgba(0, 120, 212, 0.25),
                        inset 0 0 30px rgba(0, 120, 212, 0.1);
        }

        .app-item.active::before {
            background: linear-gradient(180deg, 
                        var(--accent-color) 0%, 
                        color-mix(in srgb, var(--accent-color) 70%, white) 100%);
            width: 4px;
        }

        .app-icon {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            background: linear-gradient(135deg,
                        rgba(255, 255, 255, 0.1) 0%,
                        rgba(255, 255, 255, 0.05) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15),
                        inset 0 1px 0 rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .app-item:hover .app-icon {
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2),
                        inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        /* Remove app-info, app-name, and app-url styles for minimalistic design */

        .app-badge {
            background: #ff4444;
            color: white;
            border-radius: 50%;
            padding: 2px 4px;
            font-size: 8px;
            font-weight: 600;
            min-width: 12px;
            text-align: center;
            position: absolute;
            top: -2px;
            right: -2px;
        }

        /* Tab Bar Styles */
        .tab-bar {
            display: flex;
            flex-direction: column;
            align-items: center;
            background: linear-gradient(135deg,
                        var(--panel-background-secondary) 0%,
                        color-mix(in srgb, var(--panel-background-secondary) 90%, white) 50%,
                        var(--panel-background-secondary) 100%);
            border-bottom: 1px solid var(--panel-border);
            padding: 8px 4px; /* Reduced padding for narrow design */
            position: relative;
            z-index: 100; /* Ensure tab bar stays above web content */
            overflow-y: auto;
            overflow-x: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1),
                        inset 0 1px 0 rgba(255, 255, 255, 0.05);
            box-sizing: border-box; /* Include border in height calculation */
            flex: 1; /* Allow tab bar to expand */
        }

        .tab-bar::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, 
                        transparent 0%, 
                        rgba(255, 255, 255, 0.1) 50%, 
                        transparent 100%);
        }

        .tabs-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            gap: 8px;
            scrollbar-width: none;
            -ms-overflow-style: none;
            position: relative;
            z-index: 101; /* Higher than tab-bar to ensure proper layering */
            padding: 4px 0;
            width: 100%;
        }

        .tabs-container::-webkit-scrollbar {
            display: none;
        }

        .tab {
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg,
                        var(--panel-background) 0%,
                        color-mix(in srgb, var(--panel-background) 95%, white) 100%);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 8px;
            width: 40px;
            height: 40px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            z-index: 102; /* Ensure tabs stay above web content and other elements */
            margin: 0 4px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1),
                        inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        .tab::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: transparent;
            transition: all 0.3s ease;
        }

        .tab:hover {
            background: linear-gradient(135deg,
                        var(--app-item-hover) 0%,
                        color-mix(in srgb, var(--app-item-hover) 80%, white) 100%);
            transform: translateY(-1px) scale(1.05);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15),
                        0 2px 6px rgba(0, 0, 0, 0.1),
                        inset 0 1px 0 rgba(255, 255, 255, 0.15);
        }

        .tab:hover::before {
            background: linear-gradient(90deg, 
                        var(--accent-color) 0%, 
                        color-mix(in srgb, var(--accent-color) 70%, white) 100%);
        }

        .tab.active {
            background: linear-gradient(135deg,
                        var(--accent-color) 0%,
                        color-mix(in srgb, var(--accent-color) 85%, white) 100%);
            color: var(--button-text);
            border-color: var(--accent-color);
            box-shadow: 0 4px 16px rgba(0, 120, 212, 0.35),
                        0 2px 8px rgba(0, 120, 212, 0.2),
                        inset 0 1px 0 rgba(255, 255, 255, 0.25);
            transform: translateY(-1px) scale(1.1);
        }

        .tab.active::before {
            background: linear-gradient(90deg, 
                        rgba(255, 255, 255, 0.8) 0%, 
                        rgba(255, 255, 255, 0.6) 50%,
                        rgba(255, 255, 255, 0.8) 100%);
        }

        .tab-favicon {
            width: 24px;
            height: 24px;
            flex-shrink: 0;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        }

        /* Remove tab-title styles as we're using icon-only design */

        .tab-close {
            width: 16px;
            height: 16px;
            border: none;
            background: rgba(255, 255, 255, 0.1);
            color: inherit;
            cursor: pointer;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            opacity: 0;
            transition: all 0.3s ease;
            flex-shrink: 0;
            position: absolute;
            top: -4px;
            right: -4px;
        }

        .tab:hover .tab-close {
            opacity: 1;
        }

        .tab-close:hover {
            opacity: 1;
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.2);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
        }

        .tab.active .tab-close:hover {
            background: rgba(0, 0, 0, 0.2);
        }

        .tab-loading {
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            animation: spin 1s linear infinite;
            flex-shrink: 0;
            color: var(--accent-color);
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .new-tab-button {
            width: 40px;
            height: 40px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            background: linear-gradient(135deg,
                        var(--panel-background) 0%,
                        color-mix(in srgb, var(--panel-background) 95%, white) 100%);
            color: var(--text-primary);
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            font-weight: 500;
            margin-top: 8px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            flex-shrink: 0;
            position: relative;
            z-index: 103; /* Highest z-index for new tab button */
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15),
                        0 1px 3px rgba(0, 0, 0, 0.1),
                        inset 0 1px 0 rgba(255, 255, 255, 0.1);
            overflow: hidden;
        }

        .new-tab-button::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
            transform: translate(-50%, -50%);
            transition: all 0.4s ease;
        }

        .new-tab-button:hover {
            background: linear-gradient(135deg, 
                        var(--app-item-hover) 0%, 
                        color-mix(in srgb, var(--app-item-hover) 80%, white) 100%);
            transform: scale(1.1) translateY(-1px);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2),
                        0 2px 6px rgba(0, 0, 0, 0.1),
                        inset 0 1px 0 rgba(255, 255, 255, 0.15);
        }

        .new-tab-button:hover::before {
            width: 40px;
            height: 40px;
        }

        .new-tab-button:active {
            transform: scale(1.05) translateY(0px);
        }

        .web-content {
            flex: 1;
            background: linear-gradient(135deg,
                        #ffffff 0%,
                        color-mix(in srgb, #ffffff 98%, var(--panel-background)) 100%);
            border-radius: 0 0 0 16px;
            overflow: hidden;
            position: relative;
            z-index: 1; /* Lower z-index to stay below tab manager and other UI elements */
            box-shadow: inset 0 3px 8px rgba(0, 0, 0, 0.1),
                        inset 0 1px 2px rgba(0, 0, 0, 0.05),
                        0 -2px 4px rgba(0, 0, 0, 0.05);
            border-top: 1px solid rgba(0, 0, 0, 0.1);
        }

        /* Modal overlay styles for settings and other dialogs */
        .settings-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            z-index: 10000; /* Very high z-index to ensure it's above everything */
            display: flex;
            align-items: center;
            justify-content: center;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .settings-dialog {
            background: var(--panel-background);
            border: 2px solid var(--panel-border);
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5),
                        0 8px 24px rgba(0, 0, 0, 0.3),
                        inset 0 1px 0 rgba(255, 255, 255, 0.1);
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow: hidden;
            position: relative;
            z-index: 10001; /* Higher than overlay */
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        .web-view {
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 0 0 0 16px;
        }

        .loading-indicator {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: var(--text-secondary);
            font-size: 14px;
            font-weight: 500;
            text-align: center;
            background: rgba(255, 255, 255, 0.9);
            padding: 20px 32px;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1),
                        0 2px 6px rgba(0, 0, 0, 0.05);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .no-apps-message {
            padding: 40px 20px;
            text-align: center;
            color: rgba(255, 255, 255, 0.7);
            background: linear-gradient(135deg, 
                        rgba(255, 255, 255, 0.02) 0%, 
                        rgba(255, 255, 255, 0.05) 100%);
            border-radius: 12px;
            margin: 16px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .add-app-button {
            background: linear-gradient(135deg, 
                        var(--accent-color) 0%, 
                        color-mix(in srgb, var(--accent-color) 85%, black) 100%);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            margin-top: 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 3px 8px rgba(0, 120, 212, 0.3),
                        0 1px 3px rgba(0, 120, 212, 0.2),
                        inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .add-app-button:hover {
            background: linear-gradient(135deg, 
                        var(--button-hover) 0%, 
                        color-mix(in srgb, var(--button-hover) 85%, black) 100%);
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 6px 16px rgba(0, 120, 212, 0.4),
                        0 2px 6px rgba(0, 120, 212, 0.25),
                        inset 0 1px 0 rgba(255, 255, 255, 0.25);
        }

        .add-app-button:active {
            transform: translateY(-1px) scale(1.02);
        }

        /* Scrollbar styling */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: var(--scrollbar-track);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--scrollbar-thumb);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--scrollbar-thumb-hover);
        }
    </style>
</head>
<body>
    <div class="panel-container">
        <!-- Panel Header -->
        <div class="panel-header">
            <div class="panel-title">SideView</div>
            <div class="panel-controls">
                <button class="control-button" id="pin-button" title="Pin Panel">📌</button>
                <button class="control-button" id="settings-button" title="Settings">⚙️</button>
                <button class="control-button" id="close-button" title="Close">✕</button>
            </div>
        </div>

        <!-- Tab Bar -->
        <div class="tab-bar" id="tab-bar">
            <div class="tabs-container" id="tabs-container">
                <!-- Tabs will be dynamically added here -->
            </div>
            <button class="new-tab-button" id="new-tab-button" title="New Tab">+</button>
        </div>

        <!-- App List (Legacy - keeping for compatibility) -->
        <div class="app-list" id="app-list" style="display: none;">
            <div class="no-apps-message" id="no-apps-message">
                <div>No web apps added yet</div>
                <button class="add-app-button" id="add-app-button">Add Web App</button>
            </div>
        </div>

        <!-- Web Content Area -->
        <div class="web-content" id="web-content">
            <div class="loading-indicator" id="loading-indicator">Loading...</div>
        </div>
    </div>

    <script src="renderer.js"></script>
</body>
</html>
